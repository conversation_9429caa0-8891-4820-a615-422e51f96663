import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Target } from "lucide-react";

const AboutSection = () => {
  const features = [
    {
      icon: Bo<PERSON>,
      title: "Humanoid Robotics",
      description: "Advanced humanoid systems designed for real-world applications and human interaction."
    },
    {
      icon: Cpu,
      title: "Physical AI Systems",
      description: "Cutting-edge AI integrated with hardware for intelligent robotic behavior."
    },
    {
      icon: Zap,
      title: "General Purpose",
      description: "Versatile robots capable of performing multiple tasks across various industries."
    },
    {
      icon: Target,
      title: "Commercial Scale",
      description: "Built for mass production and deployment in commercial applications."
    }
  ];

  return (
    <section id="about" className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="text-4xl md:text-5xl font-bold">
                Who We <span className="gradient-text">Are</span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                xSpecies AI is at the forefront of robotics innovation, combining cutting-edge AI 
                with advanced hardware systems to create the next generation of general purpose 
                robots and humanoids.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid sm:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <div 
                  key={index}
                  className="flex items-start space-x-4 p-4 rounded-lg bg-card/30 border border-border/50 hover-lift"
                >
                  <div className="w-10 h-10 rounded-lg bg-gradient-primary flex items-center justify-center shrink-0">
                    <feature.icon className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div className="space-y-1">
                    <h3 className="font-semibold text-foreground">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Image */}
          <div className="relative">
            <div className="aspect-[4/3] max-w-md mx-auto rounded-2xl bg-gradient-secondary border border-border shadow-elegant overflow-hidden">
              <img 
                src="/lovable-uploads/902feb7f-1aec-415b-842f-d82b3b95a6b1.png" 
                alt="Humanoid Robot - Next Generation AI"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              <div className="absolute bottom-4 left-4 right-4 text-center">
                <h3 className="text-xl font-bold text-white mb-1">AI Humanoid</h3>
                <p className="text-white/80 text-sm">Next Generation Intelligence</p>
              </div>
            </div>
            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-8 h-8 rounded-full bg-primary/30 blur-sm"></div>
            <div className="absolute -bottom-4 -left-4 w-12 h-12 rounded-full bg-primary-glow/20 blur-md"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;