import { But<PERSON> } from "@/components/ui/button";
import { Mail, Github, Linkedin, Twitter } from "lucide-react";

const Footer = () => {
  const socialLinks = [
    { icon: Mail, href: "mailto:<EMAIL>", label: "Email" },
    { icon: Github, href: "https://github.com/xspecies-ai", label: "GitHub" },
    { icon: Linkedin, href: "https://linkedin.com/company/xspecies-ai", label: "LinkedIn" },
    { icon: Twitter, href: "https://twitter.com/xspecies_ai", label: "Twitter" },
  ];

  return (
    <footer className="relative border-t border-border bg-card/20">
      <div className="container mx-auto px-6 py-16">
        {/* Get in Touch Section */}
        <div className="text-center space-y-8 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">
            Get in <span className="gradient-text">Touch</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Ready to explore the future of robotics? Let's connect and discuss how we can 
            transform your business with our AI-powered solutions.
          </p>
          
          {/* Social Links */}
          <div className="flex items-center justify-center space-x-6">
            {socialLinks.map((social, index) => (
              <Button
                key={index}
                variant="ghost"
                size="lg"
                className="w-12 h-12 rounded-full hover:bg-primary/10 hover:text-primary transition-smooth hover-scale"
                asChild
              >
                <a 
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.label}
                >
                  <social.icon className="w-5 h-5" />
                </a>
              </Button>
            ))}
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="border-t border-border pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-gradient-primary flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-background/20"></div>
              </div>
              <span className="text-lg font-bold gradient-text">xSpecies AI</span>
            </div>

            {/* Copyright */}
            <div className="text-center text-muted-foreground">
              © 2025 xSpecies AI. All rights reserved.
            </div>

            {/* Links */}
            <div className="flex items-center space-x-6 text-sm">
              <a 
                href="/privacy" 
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Privacy Policy
              </a>
              <a 
                href="/terms" 
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;