import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON> } from "lucide-react";
import AIFace3D from "./AIFace3D";

const HeroSection = () => {
  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-hero"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-glow/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="text-center lg:text-left space-y-8">
              {/* Main Heading */}
              <div className="space-y-4">
                <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                  Building the Future of{" "}
                  <span className="gradient-text">Robotics</span>
                </h1>
                <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl lg:max-w-none mx-auto lg:mx-0 leading-relaxed">
                  Developing physical AI software and hardware systems for general purpose 
                  Robots and Humanoids in India for the world.
                </p>
              </div>
              
              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center lg:justify-start justify-center gap-4 pt-4">
                <Button 
                  size="lg" 
                  className="bg-gradient-primary hover:opacity-90 transition-smooth glow-primary group px-8 py-3"
                >
                  Learn More 
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button 
                  variant="outline" 
                  size="lg"
                  className="border-border hover:bg-accent transition-smooth px-8 py-3"
                >
                  Watch Demo
                </Button>
              </div>
            </div>
            
            {/* 3D AI Face */}
            <div className="flex justify-center lg:justify-end">
              <AIFace3D />
            </div>
          </div>

          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-16 max-w-4xl mx-auto">
            <div className="text-center space-y-2">
              <div className="text-2xl md:text-3xl font-bold text-primary">2026</div>
              <div className="text-sm text-muted-foreground">Target Launch</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-2xl md:text-3xl font-bold text-primary">2027</div>
              <div className="text-sm text-muted-foreground">Humanoid Release</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-2xl md:text-3xl font-bold text-primary">AI+</div>
              <div className="text-sm text-muted-foreground">Hardware</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-2xl md:text-3xl font-bold text-primary">India</div>
              <div className="text-sm text-muted-foreground">Made in</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;