import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

const Products = () => {
  const products = [
    {
      id: "species22",
      name: "Species22",
      icon: Cpu,
      description: "Species22 is our comprehensive technology platform that serves as the foundation for developing different versions of humanoid robots. It encompasses the entire robot technology stack, from AI and control systems to mechanical design.",
      image: "/lovable-uploads/241a8842-4c78-462a-896b-6eeea311d340.png",
      features: [
        "Modular architecture for rapid development",
        "Integrated AI and control systems", 
        "Scalable deployment framework"
      ],
      applications: [
        "Healthcare assistance and patient care",
        "Elderly care and support",
        "Industrial and commercial operations"
      ]
    },
    {
      id: "handy",
      name: "<PERSON><PERSON><PERSON><PERSON>",
      icon: Hand,
      description: "Hierarchical AI for Neural Dexterity and Yield-driven Manipulation (HANDY) is our advanced dexterous arm robot, specifically designed for pick and sort applications in logistics and warehousing environments.",
      image: "/lovable-uploads/69b37a9e-e336-4f13-a740-245de6093e9c.png",
      features: [
        "Advanced object recognition and handling",
        "Precise manipulation for various item sizes",
        "Adaptive learning for new items and tasks"
      ],
      keyFeatures: true
    },
    {
      id: "manav",
      name: "MANAV",
      icon: Bot,
      description: "MANAV is our flagship general-purpose humanoid robot, designed and built in India for global applications. It represents the culmination of our technological innovations in both business and consumer applications.",
      image: "/lovable-uploads/52a404db-854e-49fa-9622-e8e712813b20.png",
      features: [
        "General-purpose humanoid design",
        "Built in India for global applications",
        "Advanced AI integration",
        "Versatile task performance"
      ]
    },
    {
      id: "aiware",
      name: "AIWare",
      icon: Brain,
      description: "Our proprietary two-stage AI architecture integrates a Vision-Language-Action (VLA) model with Reinforcement Learning (RL), enabling robots to perform complex tasks with intelligence and precision.",
      image: "/lovable-uploads/e52cbd26-978f-4f9d-93f2-f54c87c4b4dd.png",
      features: [
        "Human Instructions & Demonstrations",
        "Vision-Language Pre-training",
        "Self-Supervision Learning",
        "Robot Action as Language Model",
        "Reinforcement Learning Optimization",
        "Sim2Real Transfer"
      ],
      isFramework: true
    }
  ];

  return (
    <div className="min-h-screen bg-background pb-20 md:pb-0">
      <Header />
      
      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-hero opacity-30"></div>
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex justify-center mb-6">
              <Link to="/">
                <Button variant="outline" className="mb-4">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              Our <span className="gradient-text">Products</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Cutting-edge robotics solutions that bridge the gap between artificial intelligence 
              and physical world applications, designed for the future of human-robot collaboration.
            </p>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-24 relative">
        <div className="container mx-auto px-6">
          <div className="grid gap-16">
            {products.map((product, index) => (
              <Card key={product.id} className="overflow-hidden border-border/50 bg-card/30 backdrop-blur-sm">
                <div className={`grid lg:grid-cols-2 gap-8 ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                  {/* Product Image */}
                  <div className={`relative ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                    <div className="aspect-[4/3] rounded-xl bg-gradient-secondary border border-border shadow-elegant overflow-hidden">
                      <img 
                        src={product.image} 
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                    </div>
                    {/* Decorative elements */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 rounded-full bg-primary/30 blur-sm"></div>
                    <div className="absolute -bottom-4 -left-4 w-12 h-12 rounded-full bg-primary-glow/20 blur-md"></div>
                  </div>

                  {/* Product Content */}
                  <div className={`space-y-6 ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                    <CardHeader className="p-0">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 rounded-lg bg-gradient-primary flex items-center justify-center">
                          <product.icon className="w-6 h-6 text-primary-foreground" />
                        </div>
                        <div>
                          <CardTitle className="text-3xl md:text-4xl font-bold">
                            {product.name}
                          </CardTitle>
                          {product.isFramework && (
                            <Badge variant="secondary" className="mt-2">AI Architecture</Badge>
                          )}
                        </div>
                      </div>
                      <CardDescription className="text-base leading-relaxed">
                        {product.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="p-0 space-y-6">
                      {/* Features */}
                      <div>
                        <h4 className="text-lg font-semibold mb-3 text-foreground">
                          {product.keyFeatures ? 'Key Features' : 
                           product.isFramework ? 'Two-Stage Learning Framework' : 'Platform Features'}
                        </h4>
                        <ul className="space-y-2">
                          {product.features.map((feature, idx) => (
                            <li key={idx} className="flex items-start space-x-3">
                              <div className="w-2 h-2 rounded-full bg-primary mt-2 shrink-0"></div>
                              <span className="text-muted-foreground">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Applications (only for Species22) */}
                      {product.applications && (
                        <div>
                          <h4 className="text-lg font-semibold mb-3 text-foreground">Applications</h4>
                          <ul className="space-y-2">
                            {product.applications.map((app, idx) => (
                              <li key={idx} className="flex items-start space-x-3">
                                <div className="w-2 h-2 rounded-full bg-accent mt-2 shrink-0"></div>
                                <span className="text-muted-foreground">{app}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Products;