const VisionSection = () => {
  return (
    <section id="vision" className="py-24 relative">
      <div className="absolute inset-0 bg-gradient-hero"></div>
      <div className="container mx-auto px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center space-y-12">
          {/* Section Header */}
          <div className="space-y-4">
            <h2 className="text-4xl md:text-5xl font-bold">
              Our <span className="gradient-text">Vision</span>
            </h2>
            <p className="text-lg text-muted-foreground leading-relaxed">
              We envision a future where human capabilities are augmented beyond current mental 
              and physical limits with the aid of AI, ushering in a new era of human progress and 
              prosperity.
            </p>
          </div>

          {/* Mission Statement */}
          <div className="bg-card/30 border border-border rounded-2xl p-8 md:p-12 backdrop-blur-sm shadow-card">
            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-center">Our Mission</h3>
              <p className="text-lg text-muted-foreground leading-relaxed text-center max-w-3xl mx-auto">
                Our mission is to develop a foundational, end-to-end technology stack for general 
                purpose robots that will be manufactured and deployed at scale in commercial 
                applications by 2026 to significantly improve operational productivity and efficiency. 
                This will culminate into the development and launch of our fully autonomous 
                Humanoid for general purpose use in both business as well as consumer applications 
                by the year 2027.
              </p>
            </div>
          </div>

          {/* Timeline */}
          <div className="grid md:grid-cols-2 gap-8 pt-8">
            <div className="bg-card/20 border border-border rounded-xl p-6 hover-lift">
              <div className="space-y-4">
                <div className="text-3xl font-bold gradient-text">2026</div>
                <h4 className="text-xl font-semibold">Commercial Deployment</h4>
                <p className="text-muted-foreground">
                  Launch of our foundational technology stack for general purpose robots 
                  in commercial applications.
                </p>
              </div>
            </div>
            <div className="bg-card/20 border border-border rounded-xl p-6 hover-lift">
              <div className="space-y-4">
                <div className="text-3xl font-bold gradient-text">2027</div>
                <h4 className="text-xl font-semibold">Humanoid Launch</h4>
                <p className="text-muted-foreground">
                  Introduction of fully autonomous humanoids for both business 
                  and consumer applications worldwide.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default VisionSection;