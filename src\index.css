@tailwind base;
@tailwind components;
@tailwind utilities;

/* xSpecies AI Design System - Futuristic & Modern */

@layer base {
  :root {
    /* Dark theme with futuristic accents */
    --background: 220 15% 8%;
    --foreground: 210 40% 98%;

    --card: 220 15% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 220 15% 10%;
    --popover-foreground: 210 40% 98%;

    /* Purple-blue gradient primary */
    --primary: 259 94% 51%;
    --primary-foreground: 210 40% 98%;
    --primary-glow: 259 94% 65%;

    /* Subtle secondary */
    --secondary: 220 15% 15%;
    --secondary-foreground: 210 40% 98%;

    /* Muted elements */
    --muted: 220 15% 20%;
    --muted-foreground: 215 20% 65%;

    /* Accent colors */
    --accent: 220 15% 18%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 15% 25%;
    --input: 220 15% 18%;
    --ring: 259 94% 51%;

    /* Custom design tokens */
    --gradient-primary: linear-gradient(135deg, hsl(259 94% 51%), hsl(220 94% 51%));
    --gradient-secondary: linear-gradient(135deg, hsl(220 15% 12%), hsl(220 15% 18%));
    --gradient-hero: linear-gradient(135deg, hsl(259 94% 51% / 0.1), hsl(220 94% 51% / 0.05));
    
    --shadow-glow: 0 0 40px hsl(259 94% 51% / 0.3);
    --shadow-card: 0 10px 30px hsl(220 15% 5% / 0.5);
    --shadow-elegant: 0 20px 50px hsl(220 15% 5% / 0.7);
    
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }
}

@layer components {
  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }
  
  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }
  
  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: var(--shadow-glow);
  }
  
  .shadow-card {
    box-shadow: var(--shadow-card);
  }
  
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: var(--transition-smooth);
  }
  
  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* Hover animations */
  .hover-lift {
    @apply transition-smooth hover:-translate-y-2 hover:shadow-elegant;
  }
  
  .hover-glow {
    @apply transition-smooth hover:glow-primary;
  }
  
  .hover-scale {
    @apply transition-smooth hover:scale-105;
  }

  /* Animated gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary via-primary-glow to-primary bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }
  
  @keyframes gradient-shift {
    0%, 100% { background-position: 200% 50%; }
    50% { background-position: 0% 50%; }
  }
}