import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Calendar, Clock } from "lucide-react";

const BlogSection = () => {
  const blogPosts = [
    {
      title: "Why the Future of Robotics Is Not Just Industrial—It's Personal",
      excerpt: "For decades, robots have been confined to factories. But the next frontier lies in our homes, hospitals, offices, and communities...",
      date: "December 15, 2024",
      readTime: "5 min read",
      category: "Future Tech",
      image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=250&fit=crop"
    },
    {
      title: "Building Intelligence into Movement: The Species AI Approach", 
      excerpt: "One of the hardest problems in robotics is manipulation. At Species AI, we're solving this with AIWare—our unified AI platform...",
      date: "December 12, 2024",
      readTime: "7 min read",
      category: "Technology",
      image: "/lovable-uploads/a155def6-a5e4-4259-a31c-2fc9b0962620.png"
    },
    {
      title: "Why India Needs Its Own Humanoid Robot",
      excerpt: "India is on the cusp of a demographic transformation. By 2050, over 140 million Indians will be over the age of 60...",
      date: "December 10, 2024", 
      readTime: "6 min read",
      category: "Industry",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop"
    }
  ];

  return (
    <section id="blog" className="py-24">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-4xl md:text-5xl font-bold">
            Latest <span className="gradient-text">Updates</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Stay updated with our latest insights, developments, and breakthrough innovations 
            in AI and robotics technology.
          </p>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <Card key={index} className="group bg-card/30 border-border hover-lift overflow-hidden">
              <div className="aspect-video bg-gradient-secondary relative overflow-hidden">
                <img 
                  src={post.image} 
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 text-xs font-medium bg-primary/90 text-primary-foreground rounded-full">
                    {post.category}
                  </span>
                </div>
              </div>
              
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{post.date}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{post.readTime}</span>
                  </div>
                </div>
                
                <h3 className="text-xl font-bold group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                
                <p className="text-muted-foreground line-clamp-3">
                  {post.excerpt}
                </p>
                
                <Button 
                  variant="ghost" 
                  className="group/btn p-0 h-auto text-primary hover:text-primary-glow transition-colors"
                >
                  Read More 
                  <ArrowRight className="ml-1 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Button 
            variant="outline" 
            size="lg"
            className="border-border hover:bg-accent transition-smooth"
          >
            View All Articles
            <ArrowRight className="ml-2 w-4 h-4" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;