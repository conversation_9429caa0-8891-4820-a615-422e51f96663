import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navItems = [
    { name: "About", href: "/#about", isRoute: false },
    { name: "Products", href: "/products", isRoute: true },
    { name: "Vision", href: "/#vision", isRoute: false },
    { name: "Blog", href: "/#blog", isRoute: false },
    { name: "Contact", href: "/#contact", isRoute: false },
  ];

  return (
    <>
      {/* Desktop Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border/50 shadow-sm">
        <div className="container mx-auto px-6 py-4">
          <nav className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-smooth">
              <div className="w-10 h-10 rounded-xl bg-gradient-primary flex items-center justify-center shadow-lg">
                <div className="w-5 h-5 rounded-full bg-background/30"></div>
              </div>
              <span className="text-2xl font-bold gradient-text">xSpecies AI</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-10">
              {navItems.map((item) => 
                item.isRoute ? (
                  <Link
                    key={item.name}
                    to={item.href}
                    className="text-muted-foreground hover:text-foreground transition-smooth relative group font-medium"
                  >
                    {item.name}
                    <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full rounded-full"></span>
                  </Link>
                ) : (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-muted-foreground hover:text-foreground transition-smooth relative group font-medium"
                  >
                    {item.name}
                    <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full rounded-full"></span>
                  </a>
                )
              )}
            </div>

            {/* Contact Button - Desktop Only */}
            <div className="hidden md:block">
              <Button 
                variant="outline" 
                className="border-primary/20 hover:border-primary hover:bg-primary/10 transition-smooth font-medium"
                onClick={() => {
                  if (location.pathname !== '/') {
                    window.location.href = '/#contact';
                  } else {
                    document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                Get in Touch
              </Button>
            </div>
          </nav>
        </div>
      </header>

      {/* Mobile Bottom Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-lg border-t border-border/50 shadow-lg">
        <div className="flex items-center justify-around py-3 px-2">
          {navItems.map((item) => 
            item.isRoute ? (
              <Link
                key={item.name}
                to={item.href}
                className="flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-primary/5 transition-smooth min-w-0"
              >
                <div className={`w-2 h-2 rounded-full transition-all ${
                  location.pathname === item.href ? 'bg-primary scale-125' : 'bg-muted-foreground/30'
                }`}></div>
                <span className={`text-xs font-medium transition-colors ${
                  location.pathname === item.href ? 'text-primary' : 'text-muted-foreground'
                }`}>
                  {item.name}
                </span>
              </Link>
            ) : (
              <a
                key={item.name}
                href={item.href}
                className="flex flex-col items-center space-y-1 p-2 rounded-lg hover:bg-primary/5 transition-smooth min-w-0"
              >
                <div className="w-2 h-2 rounded-full bg-muted-foreground/30 transition-all hover:bg-primary hover:scale-125"></div>
                <span className="text-xs font-medium text-muted-foreground hover:text-primary transition-colors">
                  {item.name}
                </span>
              </a>
            )
          )}
        </div>
      </div>
    </>
  );
};

export default Header;